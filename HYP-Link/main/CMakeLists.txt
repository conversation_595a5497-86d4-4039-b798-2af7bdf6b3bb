set(MAIN_SRCS
    "core/main.c"
    "core/app_manager.c" 
    "networking/wifi/app_wifi.c"
    "networking/sntp/sntp_client.c" 
    "networking/ppp/pppos_client.c"
    "networking/mqtt/subscription_manager.c"
    "networking/mqtt/mqtt_manager.c"
    "networking/mqtt/mqtt_manager_events.c"
    "networking/ble/ble_spp_server.c"
    "utils/map.c"
    "utils/coap_server.c" 
)
file(GLOB FILE_SRCS
    "fpwc/*.c"
)
list(APPEND MAIN_SRCS ${FILE_SRCS})

# reporting
list(APPEND MAIN_SRCS "tasks/reporting_task.c")

# OTA
list(APPEND MAIN_SRCS "tasks/ota_task.c")

# Log Upload
list(APPEND MAIN_SRCS "tasks/log_upload.c")

set(MAIN_INCLUDE_DIRS
    "."
    "core"
    "configs"
    "tasks"
    "utils"
    "networking/wifi"
    "networking/mqtt"
    "networking/ppp"
    "networking/ble"
    "fpwc"
)

set(MAIN_REQUIRES
    spiffs
    qrcode
    wifi_provisioning
    coreMQTT
    coreMQTT-Agent
    corePKCS11
    coreJSON
    backoffAlgorithm
    esp_secure_cert_mgr
    aws-iot-core-mqtt-file-streams-embedded-c
    unity
    driver
)

idf_component_register(
    SRCS
        ${MAIN_SRCS}
    INCLUDE_DIRS
        ${MAIN_INCLUDE_DIRS}
    REQUIRES
        ${MAIN_REQUIRES}
)

# OTA
target_add_binary_data(${COMPONENT_TARGET} "spiffs_image/certs/aws_codesign.crt" TEXT)

# Root Certificate
target_add_binary_data(${COMPONENT_TARGET} "spiffs_image/certs/AmazonRootCA1.pem" TEXT)

target_add_binary_data(${COMPONENT_LIB} "sensor/HYP-60.bin" BINARY)