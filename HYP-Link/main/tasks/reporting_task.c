/* Standard includes. */
#include <string.h>
#include <stdio.h>
#include <assert.h>

/* FreeRTOS includes. */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"

/* ESP-IDF includes. */
#include "esp_log.h"
#include "esp_event.h"
#include "esp_heap_caps.h"
#include "sdkconfig.h"

/* coreMQTT library include. */
#include "core_mqtt.h"

/* coreMQTT-Agent include. */
#include "core_mqtt_agent.h"

/* coreMQTT-Agent network manager include. */
#include "mqtt_manager.h"
#include "mqtt_manager_events.h"

/* coreJSON include. */
#include "core_json.h"

/* Subscription manager include. */
#include "subscription_manager.h"

/* Public function declarations */
#include "reporting_task.h"

/* Demo task configurations */
#include "reporting_task_config.h"

/* Utility functions */
#include "map.h"

/* Networking components */
#include "coap_server.h"
#include "log_upload.h"

/* Fleet provisioning demo */
#include "fleet_provisioning_with_csr.h"

/* Preprocessor definitions ***************************************************/

/* coreMQTT-Agent event group bit definitions */
#define CORE_MQTT_AGENT_CONNECTED_BIT (1 << 0)
#define CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT (1 << 1)

/* Struct definitions *********************************************************/

/**
 * @brief Defines the structure to use as the command callback context in this
 * demo.
 */
struct MQTTAgentCommandContext
{
    MQTTStatus_t xReturnStatus;
    TaskHandle_t xTaskToNotify;
    uint32_t ulNotificationValue;
    void *pArgs;
};

/* Global variables ***********************************************************/

/**
 * @brief Logging tag for ESP-IDF logging functions.
 */
const static char *TAG = "reporting_task";

/**
 * @brief The MQTT agent manages the MQTT contexts.  This set the handle to the
 * context used by this demo.
 */
extern MQTTAgentContext_t xGlobalMqttAgentContext;

/**
 * @brief The buffer to hold the topic filter. The topic is generated at runtime
 * by adding the task names.
 *
 * @note The topic strings must persist until unsubscribed.
 */
static char topicBuf[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH];

char *pcTopicBuffer = topicBuf;

bool hasThingName = false;

/**
 * @brief The event group used to manage coreMQTT-Agent events.
 */
static EventGroupHandle_t xNetworkEventGroup;

/* Static function declarations ***********************************************/

/**
 * @brief ESP Event Loop library handler for coreMQTT-Agent events.
 *
 * This handles events defined in core_mqtt_agent_events.h.
 */
static void prvCoreMqttAgentEventHandler(void *pvHandlerArg,
                                         esp_event_base_t xEventBase,
                                         int32_t lEventId,
                                         void *pvEventData);

/**
 * @brief Passed into MQTTAgent_Subscribe() as the callback to execute when the
 * broker ACKs the SUBSCRIBE message.  Its implementation sends a notification
 * to the task that called MQTTAgent_Subscribe() to let the task know the
 * SUBSCRIBE operation completed.  It also sets the xReturnStatus of the
 * structure passed in as the command's context to the value of the
 * xReturnStatus parameter - which enables the task to check the status of the
 * operation.
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pxCommandContext Context of the initial command.
 * @param[in].xReturnStatus The result of the command.
 */
static void prvSubscribeCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                        MQTTAgentReturnInfo_t *pxReturnInfo);

/**
 * @brief Passed into MQTTAgent_Publish() as the callback to execute when the
 * broker ACKs the PUBLISH message.  Its implementation sends a notification
 * to the task that called MQTTAgent_Publish() to let the task know the
 * PUBLISH operation completed.  It also sets the xReturnStatus of the
 * structure passed in as the command's context to the value of the
 * xReturnStatus parameter - which enables the task to check the status of the
 * operation.
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pxCommandContext Context of the initial command.
 * @param[in].xReturnStatus The result of the command.
 */
static void prvPublishCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                      MQTTAgentReturnInfo_t *pxReturnInfo);

/**
 * @brief Called by the task to wait for a notification from a callback function
 * after the task first executes either MQTTAgent_Publish()* or
 * MQTTAgent_Subscribe().
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pxCommandContext Context of the initial command.
 * @param[out] pulNotifiedValue The task's notification value after it receives
 * a notification from the callback.
 *
 * @return pdTRUE if the task received a notification, otherwise pdFALSE.
 */
static BaseType_t prvWaitForCommandAcknowledgment(uint32_t *pulNotifiedValue);

/**
 * @brief Passed into MQTTAgent_Subscribe() as the callback to execute when
 * there is an incoming publish on the topic being subscribed to.  Its
 * implementation just logs information about the incoming publish including
 * the publish messages source topic and payload.
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pvIncomingPublishCallbackContext Context of the initial command.
 * @param[in] pxPublishInfo Deserialized publish.
 */
static void prvIncomingPublishCallback(void *pvIncomingPublishCallbackContext,
                                       MQTTPublishInfo_t *pxPublishInfo);

/**
 * @brief Subscribe to the topic the demo task will also publish to - that
 * results in all outgoing publishes being published back to the task
 * (effectively echoed back).
 *
 * @param[in] xQoS The quality of service (QoS) to use.  Can be zero or one
 * for all MQTT brokers.  Can also be QoS2 if supported by the broker.  AWS IoT
 * does not support QoS2.
 */
static bool prvSubscribeToTopic(MQTTQoS_t xQoS,
                                char *pcTopicFilter);

/**
 * @brief The function that implements the task demonstrated by this file.
 */
static void prvTempSubPubAndLEDControlTask(void *pvParameters);

/* Static function definitions ************************************************/

static void prvPublishCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                      MQTTAgentReturnInfo_t *pxReturnInfo)
{
    /* Store the result in the application defined context so the task that
     * initiated the publish can check the operation's status. */
    pxCommandContext->xReturnStatus = pxReturnInfo->returnCode;

    if (pxCommandContext->xTaskToNotify != NULL)
    {
        /* Send the context's ulNotificationValue as the notification value so
         * the receiving task can check the value it set in the context matches
         * the value it receives in the notification. */
        xTaskNotify(pxCommandContext->xTaskToNotify,
                    pxCommandContext->ulNotificationValue,
                    eSetValueWithOverwrite);
    }
}

static void prvSubscribeCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                        MQTTAgentReturnInfo_t *pxReturnInfo)
{
    bool xSubscriptionAdded = false;
    MQTTAgentSubscribeArgs_t *pxSubscribeArgs = (MQTTAgentSubscribeArgs_t *)pxCommandContext->pArgs;

    /* Store the result in the application defined context so the task that
     * initiated the subscribe can check the operation's status.  Also send the
     * status as the notification value.  These things are just done for
     * demonstration purposes. */
    pxCommandContext->xReturnStatus = pxReturnInfo->returnCode;

    /* Check if the subscribe operation is a success. Only one topic is
     * subscribed by this demo. */
    if (pxReturnInfo->returnCode == MQTTSuccess)
    {
        /* Add subscription so that incoming publishes are routed to the application
         * callback. */
        xSubscriptionAdded = addSubscription((SubscriptionElement_t *)xGlobalMqttAgentContext.pIncomingCallbackContext,
                                             pxSubscribeArgs->pSubscribeInfo->pTopicFilter,
                                             pxSubscribeArgs->pSubscribeInfo->topicFilterLength,
                                             prvIncomingPublishCallback,
                                             NULL);

        if (xSubscriptionAdded == false)
        {
            ESP_LOGE(TAG,
                     "Failed to register an incoming publish callback for topic %.*s.",
                     pxSubscribeArgs->pSubscribeInfo->topicFilterLength,
                     pxSubscribeArgs->pSubscribeInfo->pTopicFilter);
        }
    }

    xTaskNotify(pxCommandContext->xTaskToNotify,
                (uint32_t)(pxReturnInfo->returnCode),
                eSetValueWithOverwrite);
}

static BaseType_t prvWaitForCommandAcknowledgment(uint32_t *pulNotifiedValue)
{
    BaseType_t xReturn;

    /* Wait for this task to get notified, passing out the value it gets
     * notified with. */
    xReturn = xTaskNotifyWait(0,
                              0,
                              pulNotifiedValue,
                              portMAX_DELAY);
    return xReturn;
}

static void prvParseIncomingPublish(char *publishPayload, size_t publishPayloadLength)
{
    char *outValue = NULL;
    uint32_t outValueLength = 0U;
    JSONStatus_t result = JSONSuccess;
    char deviceId[18];            // The length of the MAC address is 17 + 1 (for the terminator)
    uint32_t intervalSeconds = 0; // Used to store sleep time

    result = JSON_Validate((const char *)publishPayload, publishPayloadLength);

    if (result == JSONSuccess)
    {
        // extract deviceId
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "sen",
                             sizeof("sen") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // 将字符串终止符添加到 outValue 中
            char valueBuffer[outValueLength + 1];
            strncpy(valueBuffer, outValue, outValueLength);
            valueBuffer[outValueLength] = '\0';

            // 转换字符串为浮点数
            sen = strtof(valueBuffer, NULL);
            return;
        }

        // extract deviceId
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "deviceId",
                             sizeof("deviceId") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // Copy the deviceId value
            strncpy(deviceId, outValue, sizeof(deviceId) - 1);
            deviceId[sizeof(deviceId) - 2] = '\0'; // Ensures null termination
        }
        else
        {
            return;
        }

        // extract intervalSeconds
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "updateIntervalSeconds",
                             sizeof("updateIntervalSeconds") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // Convert intervalSeconds to an integer
            intervalSeconds = (uint32_t)strtoul(outValue, NULL, 10);
        }
        else
        {
            return;
        }

        // Call addOrUpdateSleepTime to update the sleep time
        addOrUpdateSleepTime(deviceId, intervalSeconds);
    }
    else
    {
        ESP_LOGE(TAG, "The JSON document is invalid!");
        return;
    }
}

static void prvIncomingPublishCallback(void *pvIncomingPublishCallbackContext,
                                       MQTTPublishInfo_t *pxPublishInfo)
{
    static char cTerminatedString[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH];

    (void)pvIncomingPublishCallbackContext;

    /* Create a message that contains the incoming MQTT payload to the logger,
     * terminating the string first. */
    if (pxPublishInfo->payloadLength < temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH)
    {
        memcpy((void *)cTerminatedString, pxPublishInfo->pPayload, pxPublishInfo->payloadLength);
        cTerminatedString[pxPublishInfo->payloadLength] = 0x00;
    }
    else
    {
        memcpy((void *)cTerminatedString, pxPublishInfo->pPayload, temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH);
        cTerminatedString[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH - 1] = 0x00;
    }

    // ESP_LOGI( TAG,
    //           "Received incoming publish message %s",
    //           cTerminatedString );

    ESP_LOGI(TAG, "Received incoming publish message");

    prvParseIncomingPublish((char *)pxPublishInfo->pPayload, pxPublishInfo->payloadLength);
}

static bool prvSubscribeToTopic(MQTTQoS_t xQoS,
                                char *pcTopicFilter)
{
    MQTTStatus_t xCommandAdded;
    BaseType_t xCommandAcknowledged = pdFALSE;
    MQTTAgentSubscribeArgs_t xSubscribeArgs;
    MQTTSubscribeInfo_t xSubscribeInfo;
    static int32_t ulNextSubscribeMessageID = 0;
    MQTTAgentCommandContext_t xApplicationDefinedContext = {0UL};
    MQTTAgentCommandInfo_t xCommandParams = {0UL};

    /* Create a unique number of the subscribe that is about to be sent.  The number
     * is used as the command context and is sent back to this task as a notification
     * in the callback that executed upon receipt of the subscription acknowledgment.
     * That way this task can match an acknowledgment to a subscription. */
    xTaskNotifyStateClear(NULL);

    ulNextSubscribeMessageID++;

    /* Complete the subscribe information.  The topic string must persist for
     * duration of subscription! */
    xSubscribeInfo.pTopicFilter = pcTopicFilter;
    xSubscribeInfo.topicFilterLength = (uint16_t)strlen(pcTopicFilter);
    xSubscribeInfo.qos = xQoS;
    xSubscribeArgs.pSubscribeInfo = &xSubscribeInfo;
    xSubscribeArgs.numSubscriptions = 1;

    /* Complete an application defined context associated with this subscribe message.
     * This gets updated in the callback function so the variable must persist until
     * the callback executes. */
    xApplicationDefinedContext.ulNotificationValue = ulNextSubscribeMessageID;
    xApplicationDefinedContext.xTaskToNotify = xTaskGetCurrentTaskHandle();
    xApplicationDefinedContext.pArgs = (void *)&xSubscribeArgs;

    xCommandParams.blockTimeMs = temppubsubandledcontrolconfigMAX_COMMAND_SEND_BLOCK_TIME_MS;
    xCommandParams.cmdCompleteCallback = prvSubscribeCommandCallback;
    xCommandParams.pCmdCompleteCallbackContext = (void *)&xApplicationDefinedContext;

    /* Loop in case the queue used to communicate with the MQTT agent is full and
     * attempts to post to it time out.  The queue will not become full if the
     * priority of the MQTT agent task is higher than the priority of the task
     * calling this function. */
    ESP_LOGI(TAG,
             "Sending subscribe request to agent for topic filter: %s with id %d",
             pcTopicFilter,
             (int)ulNextSubscribeMessageID);

    do
    {
        xCommandAdded = MQTTAgent_Subscribe(&xGlobalMqttAgentContext,
                                            &xSubscribeArgs,
                                            &xCommandParams);
    } while (xCommandAdded != MQTTSuccess);

    /* Wait for acks to the subscribe message - this is optional but done here
     * so the code below can check the notification sent by the callback matches
     * the ulNextSubscribeMessageID value set in the context above. */
    xCommandAcknowledged = prvWaitForCommandAcknowledgment(NULL);

    /* Check both ways the status was passed back just for demonstration
     * purposes. */
    if ((xCommandAcknowledged != pdTRUE) ||
        (xApplicationDefinedContext.xReturnStatus != MQTTSuccess))
    {
        ESP_LOGE(TAG,
                 "Error or timed out waiting for ack to subscribe message topic %s",
                 pcTopicFilter);
    }
    else
    {
        ESP_LOGI(TAG,
                 "Received subscribe ack for topic %s containing ID %d",
                 pcTopicFilter,
                 (int)xApplicationDefinedContext.ulNotificationValue);
    }

    return xCommandAcknowledged;
}

void writeThingName(void)
{
    /* Create a topic name for this task to publish to. */
    snprintf(pcTopicBuffer,
             temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
             "gateway/%s",
             thingName);
    hasThingName = true;
}

static void prvTempSubPubAndLEDControlTask(void *pvParameters)
{
    MQTTPublishInfo_t xPublishInfo = {0UL};
    char payloadBuf[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH];
    MQTTAgentCommandContext_t xCommandContext;
    uint32_t ulNotification = 0U, ulValueToNotify = 0UL;
    MQTTStatus_t xCommandAdded;
    MQTTQoS_t xQoS;
    TickType_t xTicksToDelay;
    MQTTAgentCommandInfo_t xCommandParams = {0UL};
    const char *pcTaskName;
    uint32_t ulPublishPassCounts = 0;
    uint32_t ulPublishFailCounts = 0;

    pcTaskName = pcTaskGetName(xTaskGetCurrentTaskHandle());

    /* Initialize the coreMQTT-Agent event group. */
    xNetworkEventGroup = xEventGroupCreate();
    xEventGroupSetBits(xNetworkEventGroup,
                       CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);

    /* Register coreMQTT-Agent event handler. */
    xCoreMqttAgentManagerRegisterHandler(prvCoreMqttAgentEventHandler);

    xQoS = (MQTTQoS_t)temppubsubandledcontrolconfigQOS_LEVEL;

    while (!hasThingName)
    {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    /* Subscribe to the same topic to which this task will publish.  That will
     * result in each published message being published from the server back to
     * the target. */
    prvSubscribeToTopic(xQoS, pcTopicBuffer);

    /* Configure the publish operation. */
    memset((void *)&xPublishInfo, 0x00, sizeof(xPublishInfo));
    xPublishInfo.qos = xQoS;

    /* Store the handler to this task in the command context so the callback
     * that executes when the command is acknowledged can send a notification
     * back to this task. */
    memset((void *)&xCommandContext, 0x00, sizeof(xCommandContext));
    xCommandContext.xTaskToNotify = xTaskGetCurrentTaskHandle();

    xCommandParams.blockTimeMs = temppubsubandledcontrolconfigMAX_COMMAND_SEND_BLOCK_TIME_MS;
    xCommandParams.cmdCompleteCallback = prvPublishCommandCallback;
    xCommandParams.pCmdCompleteCallbackContext = &xCommandContext;

    ulValueToNotify = 0UL;

    /* For an infinite number of publishes */
    while (1)
    {
        char mac_address[MAC_ADDR_STRLEN];

        // Wait for a sensor update MAC address from the queue
        if (xQueueReceive(sensor_update_queue, mac_address, portMAX_DELAY) == pdTRUE)
        {
            /* Create a publishing topic path */
            snprintf(pcTopicBuffer,
                     temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
                     "gateway/%s/sensor/%s/measurement/current",
                     thingName,
                     mac_address);

            char *jsonData = arrayToJson(mac_address);
            // printf("JSON data: %s\n", jsonData);
            if (jsonData)
            {
                snprintf(payloadBuf,
                         temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
                         "%s",
                         jsonData);

                heap_caps_free(jsonData); // 释放动态分配的内存
            }
            else
            {
                ESP_LOGE(TAG, "Failed to convert vector to JSON for sensorMAC: %s", mac_address);
                continue;
            }

            xPublishInfo.payloadLength = (uint16_t)strlen(payloadBuf);
            xPublishInfo.pTopicName = pcTopicBuffer;
            xPublishInfo.topicNameLength = (uint16_t)strlen(pcTopicBuffer);
            xPublishInfo.pPayload = payloadBuf;
            xPublishInfo.qos = xQoS;

            /* Also store the incrementing number in the command context so it can
             * be accessed by the callback that executes when the publish operation
             * is acknowledged. */
            xCommandContext.ulNotificationValue = ulValueToNotify;

            /* Wait for coreMQTT-Agent task to have working network connection and
             * not be performing an OTA update. */
            xEventGroupWaitBits(xNetworkEventGroup,
                                CORE_MQTT_AGENT_CONNECTED_BIT | CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT,
                                pdFALSE,
                                pdTRUE,
                                portMAX_DELAY);

            /* To ensure ulNotification doesn't accidentally hold the expected value
             * as it is to be checked against the value sent from the callback.. */
            ulNotification = ~ulValueToNotify;

            xCommandAdded = MQTTAgent_Publish(&xGlobalMqttAgentContext,
                                              &xPublishInfo,
                                              &xCommandParams);
            configASSERT(xCommandAdded == MQTTSuccess);

            /* For QoS 1 and 2, wait for the publish acknowledgment.  For QoS0,
             * wait for the publish to be sent. */
            ESP_LOGI(TAG,
                     "Task %s waiting for publish %" PRIu32 " to complete.",
                     pcTaskName,
                     ulValueToNotify);

            prvWaitForCommandAcknowledgment(&ulNotification);

            /* The value received by the callback that executed when the publish was
             * acked came from the context passed into MQTTAgent_Publish() above, so
             * should match the value set in the context above. */
            if (ulNotification == ulValueToNotify)
            {
                ulPublishPassCounts++;
                ESP_LOGI(TAG,
                         "Rx'ed %s from Tx to %s (P%" PRIu32 ":F%" PRIu32 ").",
                         (xQoS == 0) ? "completion notification for QoS0 publish" : "ack for QoS1 publish",
                         pcTopicBuffer,
                         ulPublishPassCounts,
                         ulPublishFailCounts);
            }
            else
            {
                ulPublishFailCounts++;
                ESP_LOGE(TAG,
                         "Timed out Rx'ing %s from Tx to %s (P%" PRIu32 ":F%" PRIu32 ")",
                         (xQoS == 0) ? "completion notification for QoS0 publish" : "ack for QoS1 publish",
                         pcTopicBuffer,
                         ulPublishPassCounts,
                         ulPublishFailCounts);
            }

            ulValueToNotify++;
        }

        char *json_logs = get_logs_as_json();
        if (json_logs)
        {
            // 如果是空 JSON 数组 "[]", 就不输出，否则发送日志
            if (strcmp(json_logs, "[]") != 0)
            {
                /* Create a publishing topic path */
                snprintf(pcTopicBuffer,
                         temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
                         "gateway/%s/logs",
                         thingName);

                snprintf(payloadBuf,
                         temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
                         "%s",
                         json_logs);

                xPublishInfo.payloadLength = (uint16_t)strlen(payloadBuf);
                xPublishInfo.pTopicName = pcTopicBuffer;
                xPublishInfo.topicNameLength = (uint16_t)strlen(pcTopicBuffer);
                xPublishInfo.pPayload = payloadBuf;
                xPublishInfo.qos = MQTTQoS0;

                /* Also store the incrementing number in the command context so it can
                 * be accessed by the callback that executes when the publish operation
                 * is acknowledged. */
                xCommandContext.ulNotificationValue = ulValueToNotify;

                /* Wait for coreMQTT-Agent task to have working network connection and
                 * not be performing an OTA update. */
                xEventGroupWaitBits(xNetworkEventGroup,
                                    CORE_MQTT_AGENT_CONNECTED_BIT | CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT,
                                    pdFALSE,
                                    pdTRUE,
                                    portMAX_DELAY);

                /* To ensure ulNotification doesn't accidentally hold the expected value
                 * as it is to be checked against the value sent from the callback.. */
                ulNotification = ~ulValueToNotify;

                xCommandAdded = MQTTAgent_Publish(&xGlobalMqttAgentContext,
                                                  &xPublishInfo,
                                                  &xCommandParams);
                configASSERT(xCommandAdded == MQTTSuccess);

                prvWaitForCommandAcknowledgment(&ulNotification);

                /* The value received by the callback that executed when the publish was
                 * acked came from the context passed into MQTTAgent_Publish() above, so
                 * should match the value set in the context above. */
                if (ulNotification == ulValueToNotify)
                {
                    ulPublishPassCounts++;
                }
                else
                {
                    ulPublishFailCounts++;
                }
                ulValueToNotify++;
            }
            heap_caps_free(json_logs); // 释放内存
        }
    }

    vTaskDelete(NULL);
}

static void prvCoreMqttAgentEventHandler(void *pvHandlerArg,
                                         esp_event_base_t xEventBase,
                                         int32_t lEventId,
                                         void *pvEventData)
{
    (void)pvHandlerArg;
    (void)xEventBase;
    (void)pvEventData;

    switch (lEventId)
    {
    case CORE_MQTT_AGENT_CONNECTED_EVENT:
        ESP_LOGI(TAG,
                 "coreMQTT-Agent connected.");
        xEventGroupSetBits(xNetworkEventGroup,
                           CORE_MQTT_AGENT_CONNECTED_BIT);
        break;

    case CORE_MQTT_AGENT_DISCONNECTED_EVENT:
        ESP_LOGI(TAG,
                 "coreMQTT-Agent disconnected. Preventing coreMQTT-Agent "
                 "commands from being enqueued.");
        xEventGroupClearBits(xNetworkEventGroup,
                             CORE_MQTT_AGENT_CONNECTED_BIT);
        break;

    case CORE_MQTT_AGENT_OTA_STARTED_EVENT:
        ESP_LOGI(TAG,
                 "OTA started. Preventing coreMQTT-Agent commands from "
                 "being enqueued.");
        xEventGroupClearBits(xNetworkEventGroup,
                             CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);
        break;

    case CORE_MQTT_AGENT_OTA_STOPPED_EVENT:
        ESP_LOGI(TAG,
                 "OTA stopped. No longer preventing coreMQTT-Agent "
                 "commands from being enqueued.");
        xEventGroupSetBits(xNetworkEventGroup,
                           CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);
        break;

    default:
        ESP_LOGE(TAG,
                 "coreMQTT-Agent event handler received unexpected event: %" PRIu32 "",
                 lEventId);
        break;
    }
}

/* Public function definitions ************************************************/

void vStartReporting(void)
{
    // Try to create task with PSRAM stack first
    BaseType_t result = xTaskCreateWithCaps(
        prvTempSubPubAndLEDControlTask,
        "currentSensor",
        temppubsubandledcontrolconfigTASK_STACK_SIZE,
        NULL,
        temppubsubandledcontrolconfigTASK_PRIORITY,
        NULL,
        MALLOC_CAP_SPIRAM
    );

    // Fallback to internal RAM if PSRAM stack creation fails
    if (result != pdPASS) {
        ESP_LOGW("REPORTING", "Failed to create reporting task with PSRAM stack, trying internal RAM");
        xTaskCreate(prvTempSubPubAndLEDControlTask,
                    "currentSensor",
                    temppubsubandledcontrolconfigTASK_STACK_SIZE,
                    NULL,
                    temppubsubandledcontrolconfigTASK_PRIORITY,
                    NULL);
    } else {
        ESP_LOGI("REPORTING", "Reporting task created with PSRAM stack");
    }
}
