/* 标准库 */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <regex.h>

/* FreeRTOS 相关 */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/event_groups.h"

/* ESP-IDF 相关 */
#include "esp_log.h"
#include "esp_err.h"
#include "esp_heap_caps.h"

/* coreMQTT 相关 */
#include "core_mqtt.h"
#include "core_mqtt_agent.h"
#include "mqtt_manager.h"
#include "mqtt_manager_events.h"
#include "log_mqtt_manager.h"

/* 日志任务配置 */
#include "log_upload.h"

/* Fleet provisioning demo */
#include "fpwc/fleet_provisioning_with_csr.h"

/* 预定义参数 */
#define MAX_LOG_ENTRIES 5                 // 最大缓存日志条数
#define MAX_LOG_LENGTH 256                 // 每条日志最大长度
#define LOG_UPLOAD_TASK_STACK_SIZE 4096   // 日志上传任务栈大小
#define LOG_UPLOAD_TASK_PRIORITY 3        // 日志上传任务优先级
#define LOG_UPLOAD_INTERVAL_MS 10000      // 日志上传间隔（10秒）

/* 任务相关 */
static const char *TAG = "log_upload";
static SemaphoreHandle_t log_mutex;
static TaskHandle_t log_upload_task_handle = NULL;



/* 日志缓存 - 使用PSRAM */
static char (*log_buffer)[MAX_LOG_LENGTH] = NULL;
static int log_index = 0;
static int log_count = 0; // 记录当前日志条数（不会超过 MAX_LOG_ENTRIES）

/* 全局编译的正则表达式，避免重复编译 */
static regex_t ansi_regex;
static bool regex_compiled = false;

void remove_ansi_colors(char *str) {
    if (!regex_compiled) {
        if (regcomp(&ansi_regex, "\033\\[[0-9;]*m", REG_EXTENDED) == 0) {
            regex_compiled = true;
        } else {
            return; // 编译失败，直接返回
        }
    }

    char *result = str;
    regmatch_t match;

    while (regexec(&ansi_regex, result, 1, &match, 0) == 0) {
        memmove(result + match.rm_so, result + match.rm_eo, strlen(result + match.rm_eo) + 1);
    }
}

/* JSON字符串转义函数 */
static size_t escape_json_string(const char *src, char *dst, size_t dst_size) {
    size_t src_len = strlen(src);
    size_t dst_pos = 0;

    for (size_t i = 0; i < src_len && dst_pos < dst_size - 1; i++) {
        char c = src[i];

        switch (c) {
            case '"':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = '"';
                }
                break;
            case '\\':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = '\\';
                }
                break;
            case '\n':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = 'n';
                }
                break;
            case '\r':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = 'r';
                }
                break;
            case '\t':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = 't';
                }
                break;
            case '\b':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = 'b';
                }
                break;
            case '\f':
                if (dst_pos + 2 < dst_size) {
                    dst[dst_pos++] = '\\';
                    dst[dst_pos++] = 'f';
                }
                break;
            default:
                // 过滤控制字符（0x00-0x1F，除了已处理的）
                if (c >= 0x20 || c == '\0') {
                    dst[dst_pos++] = c;
                }
                break;
        }
    }

    dst[dst_pos] = '\0';
    return dst_pos;
}

static int custom_vprintf(const char *fmt, va_list args)
{
    if (!log_buffer) {
        return vprintf(fmt, args); // 如果缓冲区未初始化，直接输出
    }

    char log_entry[MAX_LOG_LENGTH];
    vsnprintf(log_entry, MAX_LOG_LENGTH, fmt, args);
    remove_ansi_colors(log_entry);

    // 获取互斥锁保护日志缓冲区
    if (log_mutex && xSemaphoreTake(log_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // **环形缓冲区策略**：如果缓冲区满了，丢弃最早的日志
        if (log_count >= MAX_LOG_ENTRIES) {
            log_index = (log_index + 1) % MAX_LOG_ENTRIES;  // 覆盖最旧的日志
        } else {
            log_count++;  // 只有缓冲区未满时才增加 log_count
        }

        // 存入新日志（会覆盖最旧的日志）
        snprintf(log_buffer[(log_index + log_count - 1) % MAX_LOG_ENTRIES], MAX_LOG_LENGTH, "%s", log_entry);

        xSemaphoreGive(log_mutex);
    }

    return vprintf(fmt, args);
}


/* 📌 **初始化日志捕获** */
void init_log_capture()
{
    // 分配日志缓冲区到PSRAM
    if (!log_buffer) {
        log_buffer = heap_caps_malloc(MAX_LOG_ENTRIES * MAX_LOG_LENGTH,
                                      MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!log_buffer) {
            // 如果PSRAM分配失败，回退到内部RAM
            log_buffer = heap_caps_malloc(MAX_LOG_ENTRIES * MAX_LOG_LENGTH,
                                          MALLOC_CAP_8BIT);
        }
        if (!log_buffer) {
            ESP_LOGE(TAG, "Failed to allocate log buffer");
            return;
        }
        ESP_LOGI(TAG, "Log buffer allocated in %s",
                 heap_caps_get_allocated_size(log_buffer) ? "PSRAM" : "Internal RAM");

        // 清零缓冲区
        memset(log_buffer, 0, MAX_LOG_ENTRIES * MAX_LOG_LENGTH);
    }

    // 创建互斥锁
    if (!log_mutex) {
        log_mutex = xSemaphoreCreateMutex();
        if (!log_mutex) {
            ESP_LOGE(TAG, "Failed to create log mutex");
            return;
        }
    }

    esp_log_set_vprintf(custom_vprintf);
    ESP_LOGI(TAG, "Log capture initialized");
}

char *get_logs_as_json() {
    if (!log_buffer || !log_mutex) {
        ESP_LOGW(TAG, "Log system not initialized");
        return NULL;
    }

    // 获取互斥锁
    if (xSemaphoreTake(log_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGW(TAG, "Failed to acquire log mutex");
        return NULL;
    }

    if (log_count == 0) {
        xSemaphoreGive(log_mutex);
        // 无日志，返回空 JSON
        char *empty_json = heap_caps_malloc(3, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (empty_json == NULL) {
            empty_json = heap_caps_malloc(3, MALLOC_CAP_8BIT); // Fallback to internal RAM
        }
        if (empty_json != NULL) {
            strcpy(empty_json, "[]");
        }
        return empty_json;
    }

    // 为JSON分配更大的缓冲区，考虑转义字符
    size_t buffer_size = MAX_LOG_ENTRIES * MAX_LOG_LENGTH * 2 + 100; // 预留转义空间
    char *json_payload = (char *)heap_caps_malloc(buffer_size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (json_payload == NULL) {
        // Fallback to internal RAM if PSRAM allocation fails
        json_payload = (char *)heap_caps_malloc(buffer_size, MALLOC_CAP_8BIT);
    }
    if (json_payload == NULL) {
        ESP_LOGE(TAG, "内存分配失败，无法生成 JSON");
        xSemaphoreGive(log_mutex);
        return NULL;
    }

    // 为转义后的字符串分配临时缓冲区
    char *escaped_buffer = (char *)heap_caps_malloc(MAX_LOG_LENGTH * 2, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (escaped_buffer == NULL) {
        escaped_buffer = (char *)heap_caps_malloc(MAX_LOG_LENGTH * 2, MALLOC_CAP_8BIT);
    }
    if (escaped_buffer == NULL) {
        ESP_LOGE(TAG, "转义缓冲区内存分配失败");
        heap_caps_free(json_payload);
        xSemaphoreGive(log_mutex);
        return NULL;
    }

    size_t used_length = 1; // `[`
    strcpy(json_payload, "[");

    // **遍历顺序调整：从最早的日志开始，保证 JSON 组成顺序正确**
    for (int i = 0; i < log_count; i++) {
        int log_pos = (log_index + i) % MAX_LOG_ENTRIES; // **从最早存入的日志开始**

        // 转义JSON字符串
        size_t escaped_len = escape_json_string(log_buffer[log_pos], escaped_buffer, MAX_LOG_LENGTH * 2);
        size_t entry_length = escaped_len + 4; // `""` + `,`

        if (used_length + entry_length >= buffer_size - 2) {
            ESP_LOGW(TAG, "日志内容过长，截断 JSON");
            break;
        }

        strcat(json_payload, "\"");
        strcat(json_payload, escaped_buffer);
        strcat(json_payload, "\"");

        if (i < log_count - 1) {
            strcat(json_payload, ",");
        }

        used_length += entry_length;
    }
    strcat(json_payload, "]");

    // 释放转义缓冲区
    heap_caps_free(escaped_buffer);

    // **读取完日志后，重置索引**
    log_index = 0;
    log_count = 0;

    xSemaphoreGive(log_mutex);
    return json_payload;
}

/* 全局变量用于跟踪上次发送时间 */
static TickType_t last_send_time = 0;

/* 检查是否需要发送日志 */
static bool should_send_logs(void)
{
    TickType_t current_time = xTaskGetTickCount();
    bool should_send = false;

    // 获取互斥锁来安全读取log_count
    if (log_mutex && xSemaphoreTake(log_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 如果日志缓冲区满了（5条），立即发送
        if (log_count >= MAX_LOG_ENTRIES) {
            should_send = true;
        }
        // 如果有日志且距离上次发送超过10秒，发送
        else if (log_count > 0 && (current_time - last_send_time) >= pdMS_TO_TICKS(LOG_UPLOAD_INTERVAL_MS)) {
            should_send = true;
        }

        xSemaphoreGive(log_mutex);
    }

    return should_send;
}

/* 更新发送时间 */
static void update_send_time(void)
{
    last_send_time = xTaskGetTickCount();
}

/* 日志上传任务 */
static void log_upload_task(void *pvParameters)
{
    (void)pvParameters;

    char *pcTopicBuffer = NULL;
    char *json_logs = NULL;

    ESP_LOGI(TAG, "Log upload task started");
    ESP_LOGI(TAG, "Current thingName: '%s'", thingName[0] ? thingName : "(empty)");

    // 分配主题缓冲区到PSRAM
    pcTopicBuffer = heap_caps_malloc(256, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (!pcTopicBuffer) {
        pcTopicBuffer = heap_caps_malloc(256, MALLOC_CAP_8BIT);
    }
    if (!pcTopicBuffer) {
        ESP_LOGE(TAG, "Failed to allocate topic buffer");
        vTaskDelete(NULL);
        return;
    }

    while (1) {
        // 检查是否需要发送日志
        if (should_send_logs()) {
            // 等待日志MQTT连接建立
            if (xLogMqttIsConnected() == pdTRUE) {
                // 获取日志数据
                json_logs = get_logs_as_json();
                if (json_logs) {
                    // 如果不是空JSON数组，则发送日志
                    if (strcmp(json_logs, "[]") != 0) {
                        // 检查thingName是否有效
                        if (thingName[0] == '\0') {
                            ESP_LOGW(TAG, "thingName is empty, using default");
                            snprintf(pcTopicBuffer, 256, "gateway/default_device/logs");
                        } else {
                            // 创建日志主题
                            snprintf(pcTopicBuffer, 256, "gateway/%s/logs", thingName);
                        }

                        ESP_LOGD(TAG, "Publishing log to topic: %s", pcTopicBuffer);

                        // 发布日志到独立的MQTT连接
                        BaseType_t result = xLogMqttPublish(pcTopicBuffer,
                                                            strlen(pcTopicBuffer),
                                                            json_logs,
                                                            strlen(json_logs));

                        if (result == pdPASS) {
                            ESP_LOGD(TAG, "Log published successfully to topic: %s", pcTopicBuffer);
                            // 发送成功后更新发送时间
                            update_send_time();
                        } else {
                            ESP_LOGW(TAG, "Failed to publish log to topic: %s", pcTopicBuffer);
                        }
                    }

                    // 释放JSON内存
                    heap_caps_free(json_logs);
                    json_logs = NULL;
                }
            } else {
                ESP_LOGD(TAG, "Log MQTT not connected, waiting...");
            }

            // 无论是否发送成功，都更新发送时间以避免频繁重试
            update_send_time();
        }

        // 短暂延迟后再次检查
        vTaskDelay(pdMS_TO_TICKS(1000)); // 1秒检查一次
    }

    // 清理资源（实际上不会到达这里）
    if (pcTopicBuffer) {
        heap_caps_free(pcTopicBuffer);
    }

    vTaskDelete(NULL);
}

/* 启动日志上传任务 */
void start_log_upload_task()
{
    if (log_upload_task_handle != NULL) {
        ESP_LOGW(TAG, "Log upload task already running");
        return;
    }

    // 尝试使用PSRAM栈创建任务
    BaseType_t result = xTaskCreateWithCaps(log_upload_task,
                                            "LogUpload",
                                            LOG_UPLOAD_TASK_STACK_SIZE,
                                            NULL,
                                            LOG_UPLOAD_TASK_PRIORITY,
                                            &log_upload_task_handle,
                                            MALLOC_CAP_SPIRAM);

    // 如果PSRAM栈创建失败，回退到内部RAM
    if (result != pdPASS) {
        ESP_LOGW(TAG, "Failed to create log upload task with PSRAM stack, trying internal RAM");
        result = xTaskCreate(log_upload_task,
                             "LogUpload",
                             LOG_UPLOAD_TASK_STACK_SIZE,
                             NULL,
                             LOG_UPLOAD_TASK_PRIORITY,
                             &log_upload_task_handle);
    } else {
        ESP_LOGI(TAG, "Log upload task created with PSRAM stack");
    }

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create log upload task");
        log_upload_task_handle = NULL;
    } else {
        ESP_LOGI(TAG, "Log upload task started successfully");
    }
}

/* 停止日志上传任务 */
void stop_log_upload_task()
{
    if (log_upload_task_handle != NULL) {
        vTaskDelete(log_upload_task_handle);
        log_upload_task_handle = NULL;
        ESP_LOGI(TAG, "Log upload task stopped");
    }
}

/* 清理日志系统资源 */
void cleanup_log_system()
{
    // 停止日志上传任务
    stop_log_upload_task();

    // 清理正则表达式
    if (regex_compiled) {
        regfree(&ansi_regex);
        regex_compiled = false;
    }

    // 清理互斥锁
    if (log_mutex) {
        vSemaphoreDelete(log_mutex);
        log_mutex = NULL;
    }

    // 清理日志缓冲区
    if (log_buffer) {
        heap_caps_free(log_buffer);
        log_buffer = NULL;
    }

    // 重置日志状态
    log_index = 0;
    log_count = 0;
    last_send_time = 0;

    ESP_LOGI(TAG, "Log system cleaned up");
}
