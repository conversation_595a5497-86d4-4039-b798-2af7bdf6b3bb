/* 标准库 */
#include <stdio.h>
#include <string.h>
#include <assert.h>

/* FreeRTOS 相关 */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"

/* ESP-IDF 相关 */
#include "esp_log.h"
#include "esp_err.h"
#include "esp_heap_caps.h"
#include "esp_event.h"

/* coreMQTT 相关 */
#include "core_mqtt.h"
#include "core_mqtt_agent.h"
#include "freertos_agent_message.h"
#include "freertos_command_pool.h"

/* 网络传输相关 */
#include "transport_interface.h"
#include "network_transport.h"
#include "backoff_algorithm.h"

/* 本地头文件 */
#include "log_mqtt_manager.h"
#include "mqtt_manager_events.h"

/* 配置文件 */
extern const char thingName[];

/* 日志标签 */
static const char *TAG = "log_mqtt_manager";

/* 网络相关 */
static NetworkContext_t *pxLogNetworkContext = NULL;
static EventGroupHandle_t xLogNetworkEventGroup = NULL;

/* MQTT Agent 相关 - 所有内存都使用PSRAM */
static MQTTAgentContext_t xLogMqttAgentContext;
static uint8_t *ucLogNetworkBuffer = NULL;
static MQTTFixedBuffer_t xLogFixedBuffer;
static MQTTAgentMessageInterface_t xLogMessageInterface;
static MQTTAgentMessageContext_t xLogCommandQueue;

/* 静态队列存储 - 使用PSRAM */
static uint8_t *pucLogQueueStorageArea = NULL;
static StaticQueue_t *pxLogQueueStructure = NULL;

/* 任务句柄 */
static TaskHandle_t xLogMqttAgentTaskHandle = NULL;
static TaskHandle_t xLogConnectionTaskHandle = NULL;

/* 连接状态 */
static bool bLogMqttConnected = false;

/* 函数声明 */
static void prvLogMqttAgentTask(void *pvParameters);
static void prvLogMqttConnectionTask(void *pvParameters);
static MQTTStatus_t prvLogMqttAgentInit(NetworkContext_t *pxNetworkContext);
static MQTTStatus_t prvLogMqttAgentConnect(bool xCleanSession);
static uint32_t prvGetTimeMs(void);
static void prvLogIncomingPublishCallback(MQTTAgentContext_t *pMqttAgentContext,
                                          uint16_t packetId,
                                          MQTTPublishInfo_t *pxPublishInfo);

/* 获取时间函数 */
static uint32_t prvGetTimeMs(void)
{
    TickType_t xTickCount = 0;
    uint32_t ulTimeMs = 0UL;

    /* Get the current tick count. */
    xTickCount = xTaskGetTickCount();

    /* Convert the ticks to milliseconds. */
    ulTimeMs = (uint32_t)xTickCount * portTICK_PERIOD_MS;

    return ulTimeMs;
}

/* 日志MQTT消息回调 */
static void prvLogIncomingPublishCallback(MQTTAgentContext_t *pMqttAgentContext,
                                          uint16_t packetId,
                                          MQTTPublishInfo_t *pxPublishInfo)
{
    (void)pMqttAgentContext;
    (void)packetId;
    (void)pxPublishInfo;
    
    /* 日志MQTT连接通常不需要处理接收的消息 */
    ESP_LOGD(TAG, "Log MQTT received message on topic: %.*s",
             (int)pxPublishInfo->topicNameLength,
             pxPublishInfo->pTopicName);
}

/* 日志MQTT Agent初始化 */
static MQTTStatus_t prvLogMqttAgentInit(NetworkContext_t *pxNetworkContext)
{
    TransportInterface_t xTransport = {0};
    MQTTStatus_t xReturn;

    /* 分配网络缓冲区到PSRAM */
    if (!ucLogNetworkBuffer) {
        ucLogNetworkBuffer = heap_caps_malloc(LOG_MQTT_AGENT_NETWORK_BUFFER_SIZE, 
                                              MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!ucLogNetworkBuffer) {
            /* 如果PSRAM分配失败，回退到内部RAM */
            ucLogNetworkBuffer = heap_caps_malloc(LOG_MQTT_AGENT_NETWORK_BUFFER_SIZE, 
                                                  MALLOC_CAP_8BIT);
        }
        if (!ucLogNetworkBuffer) {
            ESP_LOGE(TAG, "Failed to allocate log MQTT network buffer");
            return MQTTNoMemory;
        }
        ESP_LOGI(TAG, "Log MQTT network buffer allocated in %s",
                 heap_caps_get_allocated_size(ucLogNetworkBuffer) ? "PSRAM" : "Internal RAM");
    }

    /* 设置固定缓冲区 */
    xLogFixedBuffer.pBuffer = ucLogNetworkBuffer;
    xLogFixedBuffer.size = LOG_MQTT_AGENT_NETWORK_BUFFER_SIZE;

    /* 分配队列存储区域到PSRAM */
    if (!pucLogQueueStorageArea) {
        size_t queueStorageSize = LOG_MQTT_AGENT_COMMAND_QUEUE_LENGTH * sizeof(MQTTAgentCommand_t *);
        pucLogQueueStorageArea = heap_caps_malloc(queueStorageSize, 
                                                  MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!pucLogQueueStorageArea) {
            pucLogQueueStorageArea = heap_caps_malloc(queueStorageSize, MALLOC_CAP_8BIT);
        }
        if (!pucLogQueueStorageArea) {
            ESP_LOGE(TAG, "Failed to allocate log MQTT queue storage");
            return MQTTNoMemory;
        }
    }

    /* 分配队列结构到PSRAM */
    if (!pxLogQueueStructure) {
        pxLogQueueStructure = heap_caps_malloc(sizeof(StaticQueue_t), 
                                               MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!pxLogQueueStructure) {
            pxLogQueueStructure = heap_caps_malloc(sizeof(StaticQueue_t), MALLOC_CAP_8BIT);
        }
        if (!pxLogQueueStructure) {
            ESP_LOGE(TAG, "Failed to allocate log MQTT queue structure");
            return MQTTNoMemory;
        }
    }

    /* 创建命令队列 */
    xLogCommandQueue.queue = xQueueCreateStatic(LOG_MQTT_AGENT_COMMAND_QUEUE_LENGTH,
                                                sizeof(MQTTAgentCommand_t *),
                                                pucLogQueueStorageArea,
                                                pxLogQueueStructure);
    if (!xLogCommandQueue.queue) {
        ESP_LOGE(TAG, "Failed to create log MQTT command queue");
        return MQTTNoMemory;
    }

    xLogMessageInterface.pMsgCtx = &xLogCommandQueue;
    xLogMessageInterface.send = Agent_MessageSend;
    xLogMessageInterface.recv = Agent_MessageReceive;
    xLogMessageInterface.getCommand = Agent_GetCommand;
    xLogMessageInterface.releaseCommand = Agent_ReleaseCommand;

    /* 初始化任务池 */
    Agent_InitializePool();

    /* 设置传输接口 */
    xTransport.pNetworkContext = pxNetworkContext;
    xTransport.send = espTlsTransportSend;
    xTransport.recv = espTlsTransportRecv;

    /* 初始化MQTT库 */
    xReturn = MQTTAgent_Init(&xLogMqttAgentContext,
                             &xLogMessageInterface,
                             &xLogFixedBuffer,
                             &xTransport,
                             prvGetTimeMs,
                             prvLogIncomingPublishCallback,
                             NULL);

    return xReturn;
}

/* 日志MQTT连接 */
static MQTTStatus_t prvLogMqttAgentConnect(bool xCleanSession)
{
    MQTTConnectInfo_t xConnectInfo;
    MQTTStatus_t xResult;
    bool xSessionPresent = false;

    /* 设置连接信息 */
    (void)memset(&xConnectInfo, 0x00, sizeof(xConnectInfo));
    xConnectInfo.cleanSession = xCleanSession;
    xConnectInfo.pClientIdentifier = thingName;
    xConnectInfo.clientIdentifierLength = strlen(thingName);
    xConnectInfo.keepAliveSeconds = 60;

    /* 发送MQTT CONNECT包 */
    xResult = MQTT_Connect(&(xLogMqttAgentContext.mqttContext),
                           &xConnectInfo,
                           NULL,
                           5000, /* 5秒超时 */
                           &xSessionPresent);

    if (xResult == MQTTSuccess) {
        ESP_LOGI(TAG, "Log MQTT connected. Session present: %d", xSessionPresent);
        bLogMqttConnected = true;
        
        /* 设置连接事件位 */
        if (xLogNetworkEventGroup) {
            xEventGroupSetBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_CONNECTED_BIT);
            xEventGroupClearBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_DISCONNECTED_BIT);
        }
    } else {
        ESP_LOGE(TAG, "Log MQTT connection failed with status: %s", 
                 MQTT_Status_strerror(xResult));
        bLogMqttConnected = false;
    }

    return xResult;
}

/* 日志MQTT Agent任务 */
static void prvLogMqttAgentTask(void *pvParameters)
{
    MQTTStatus_t xMQTTStatus = MQTTSuccess;

    (void)pvParameters;

    ESP_LOGI(TAG, "Log MQTT Agent task started");

    do {
        /* 等待连接事件 */
        xEventGroupWaitBits(xLogNetworkEventGroup,
                            LOG_MQTT_AGENT_CONNECTED_BIT, 
                            pdFALSE, 
                            pdTRUE,
                            portMAX_DELAY);

        /* 运行MQTT Agent命令循环 */
        xMQTTStatus = MQTTAgent_CommandLoop(&xLogMqttAgentContext);

        /* 处理断开连接 */
        if (xMQTTStatus == MQTTSuccess) {
            ESP_LOGI(TAG, "Log MQTT disconnected from broker");
        } else {
            ESP_LOGE(TAG, "Log MQTT Agent command loop failed: %s", 
                     MQTT_Status_strerror(xMQTTStatus));
        }

        bLogMqttConnected = false;
        
        /* 设置断开连接事件位 */
        if (xLogNetworkEventGroup) {
            xEventGroupClearBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_CONNECTED_BIT);
            xEventGroupSetBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_DISCONNECTED_BIT);
        }

        /* 短暂延迟后重试 */
        vTaskDelay(pdMS_TO_TICKS(1000));

    } while (1);
}

/* 日志MQTT连接任务 */
static void prvLogMqttConnectionTask(void *pvParameters)
{
    (void)pvParameters;

    static bool xCleanSession = true;
    BackoffAlgorithmContext_t xReconnectParams;
    BaseType_t xBackoffRet;
    TlsTransportStatus_t xTlsRet;
    MQTTStatus_t eMqttRet;

    ESP_LOGI(TAG, "Log MQTT connection task started");

    while (1) {
        /* 等待主网络连接建立 */
        extern EventGroupHandle_t xNetworkEventGroup;
        if (xNetworkEventGroup) {
            xEventGroupWaitBits(xNetworkEventGroup,
                                WIFI_CONNECTED_BIT | ETHERNET_CONNECTED_BIT | PPP_CONNECTED_BIT,
                                pdFALSE,
                                pdFALSE,
                                portMAX_DELAY);
        } else {
            vTaskDelay(pdMS_TO_TICKS(5000));
        }

        /* 设置断开连接事件位 */
        if (xLogNetworkEventGroup) {
            xEventGroupSetBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_DISCONNECTED_BIT);
            xEventGroupClearBits(xLogNetworkEventGroup, LOG_MQTT_AGENT_CONNECTED_BIT);
        }

        xBackoffRet = pdFAIL;
        xTlsRet = TLS_TRANSPORT_CONNECT_FAILURE;
        eMqttRet = MQTTBadParameter;

        /* 如果之前有连接，先断开 */
        if ((pxLogNetworkContext != NULL) && (pxLogNetworkContext->pxTls != NULL)) {
            xTlsDisconnect(pxLogNetworkContext);
            ESP_LOGI(TAG, "Log TLS connection was disconnected");
        }

        /* 初始化重连参数 */
        BackoffAlgorithm_InitializeParams(&xReconnectParams,
                                          1000,  /* 1秒基础延迟 */
                                          30000, /* 30秒最大延迟 */
                                          10);   /* 最大重试次数 */

        do {
            /* 建立TLS连接 */
            xTlsRet = xTlsConnect(pxLogNetworkContext);

            if (xTlsRet == TLS_TRANSPORT_SUCCESS) {
                ESP_LOGI(TAG, "Log TLS connection established");

                /* 连接MQTT */
                eMqttRet = prvLogMqttAgentConnect(xCleanSession);

                if (eMqttRet != MQTTSuccess) {
                    ESP_LOGE(TAG, "Log MQTT connection failed: %s",
                             MQTT_Status_strerror(eMqttRet));

                    /* 断开TLS连接 */
                    xTlsDisconnect(pxLogNetworkContext);
                }
            } else {
                ESP_LOGE(TAG, "Log TLS connection failed");
            }

            /* 如果连接失败，执行退避算法 */
            if ((xTlsRet != TLS_TRANSPORT_SUCCESS) || (eMqttRet != MQTTSuccess)) {
                uint16_t usNextRetryBackOff = 0U;
                BackoffAlgorithmStatus_t xBackoffAlgStatus = BackoffAlgorithmSuccess;

                xBackoffAlgStatus = BackoffAlgorithm_GetNextBackoff(&xReconnectParams,
                                                                    esp_random(),
                                                                    &usNextRetryBackOff);

                if (xBackoffAlgStatus == BackoffAlgorithmRetriesExhausted) {
                    ESP_LOGE(TAG, "Log MQTT connection retries exhausted");
                    xBackoffRet = pdFAIL;
                } else if (xBackoffAlgStatus == BackoffAlgorithmSuccess) {
                    ESP_LOGI(TAG, "Log MQTT retry attempt %lu after %u ms",
                             xReconnectParams.attemptsDone, usNextRetryBackOff);
                    vTaskDelay(pdMS_TO_TICKS(usNextRetryBackOff));
                    xBackoffRet = pdPASS;
                }
            }

        } while ((xTlsRet != TLS_TRANSPORT_SUCCESS || eMqttRet != MQTTSuccess) &&
                 (xBackoffRet == pdPASS));

        /* 如果连接成功，等待断开连接事件 */
        if ((xTlsRet == TLS_TRANSPORT_SUCCESS) && (eMqttRet == MQTTSuccess)) {
            xEventGroupWaitBits(xLogNetworkEventGroup,
                                LOG_MQTT_AGENT_DISCONNECTED_BIT,
                                pdFALSE,
                                pdTRUE,
                                portMAX_DELAY);
        }

        /* 短暂延迟后重新开始连接循环 */
        vTaskDelay(pdMS_TO_TICKS(2000));
    }
}

/* 公共函数实现 */

BaseType_t xLogMqttManagerStart(NetworkContext_t *pxNetworkContextIn)
{
    BaseType_t xRet = pdPASS;
    MQTTStatus_t eMqttRet;

    if (pxNetworkContextIn == NULL) {
        ESP_LOGE(TAG, "Network context pointer is null");
        return pdFAIL;
    }

    pxLogNetworkContext = pxNetworkContextIn;

    /* 创建事件组 */
    xLogNetworkEventGroup = xEventGroupCreate();
    if (xLogNetworkEventGroup == NULL) {
        ESP_LOGE(TAG, "Failed to create log MQTT event group");
        return pdFAIL;
    }

    /* 初始化日志MQTT Agent */
    eMqttRet = prvLogMqttAgentInit(pxLogNetworkContext);
    if (eMqttRet != MQTTSuccess) {
        ESP_LOGE(TAG, "Failed to initialize log MQTT Agent: %s",
                 MQTT_Status_strerror(eMqttRet));
        return pdFAIL;
    }

    /* 创建日志MQTT Agent任务 - 使用PSRAM栈 */
    xRet = xTaskCreateWithCaps(prvLogMqttAgentTask,
                               "LogMQTTAgent",
                               LOG_MQTT_AGENT_TASK_STACK_SIZE,
                               NULL,
                               LOG_MQTT_AGENT_TASK_PRIORITY,
                               &xLogMqttAgentTaskHandle,
                               MALLOC_CAP_SPIRAM);

    if (xRet != pdPASS) {
        ESP_LOGW(TAG, "Failed to create log MQTT Agent task with PSRAM, trying internal RAM");
        xRet = xTaskCreate(prvLogMqttAgentTask,
                           "LogMQTTAgent",
                           LOG_MQTT_AGENT_TASK_STACK_SIZE,
                           NULL,
                           LOG_MQTT_AGENT_TASK_PRIORITY,
                           &xLogMqttAgentTaskHandle);
    } else {
        ESP_LOGI(TAG, "Log MQTT Agent task created with PSRAM stack");
    }

    if (xRet != pdPASS) {
        ESP_LOGE(TAG, "Failed to create log MQTT Agent task");
        return pdFAIL;
    }

    /* 创建日志MQTT连接任务 - 使用PSRAM栈 */
    xRet = xTaskCreateWithCaps(prvLogMqttConnectionTask,
                               "LogMQTTConn",
                               LOG_CONNECTION_TASK_STACK_SIZE,
                               NULL,
                               LOG_CONNECTION_TASK_PRIORITY,
                               &xLogConnectionTaskHandle,
                               MALLOC_CAP_SPIRAM);

    if (xRet != pdPASS) {
        ESP_LOGW(TAG, "Failed to create log MQTT connection task with PSRAM, trying internal RAM");
        xRet = xTaskCreate(prvLogMqttConnectionTask,
                           "LogMQTTConn",
                           LOG_CONNECTION_TASK_STACK_SIZE,
                           NULL,
                           LOG_CONNECTION_TASK_PRIORITY,
                           &xLogConnectionTaskHandle);
    } else {
        ESP_LOGI(TAG, "Log MQTT connection task created with PSRAM stack");
    }

    if (xRet != pdPASS) {
        ESP_LOGE(TAG, "Failed to create log MQTT connection task");
        /* 清理已创建的任务 */
        if (xLogMqttAgentTaskHandle) {
            vTaskDelete(xLogMqttAgentTaskHandle);
            xLogMqttAgentTaskHandle = NULL;
        }
        return pdFAIL;
    }

    ESP_LOGI(TAG, "Log MQTT manager started successfully");
    return pdPASS;
}

BaseType_t xLogMqttManagerStop(void)
{
    BaseType_t xRet = pdPASS;

    ESP_LOGI(TAG, "Stopping log MQTT manager");

    /* 停止任务 */
    if (xLogMqttAgentTaskHandle) {
        vTaskDelete(xLogMqttAgentTaskHandle);
        xLogMqttAgentTaskHandle = NULL;
    }

    if (xLogConnectionTaskHandle) {
        vTaskDelete(xLogConnectionTaskHandle);
        xLogConnectionTaskHandle = NULL;
    }

    /* 断开MQTT连接 */
    if (bLogMqttConnected) {
        MQTT_Disconnect(&(xLogMqttAgentContext.mqttContext));
        bLogMqttConnected = false;
    }

    /* 断开TLS连接 */
    if ((pxLogNetworkContext != NULL) && (pxLogNetworkContext->pxTls != NULL)) {
        xTlsDisconnect(pxLogNetworkContext);
    }

    /* 清理事件组 */
    if (xLogNetworkEventGroup) {
        vEventGroupDelete(xLogNetworkEventGroup);
        xLogNetworkEventGroup = NULL;
    }

    /* 清理队列 */
    if (xLogCommandQueue.queue) {
        vQueueDelete(xLogCommandQueue.queue);
        xLogCommandQueue.queue = NULL;
    }

    /* 释放内存 */
    if (ucLogNetworkBuffer) {
        heap_caps_free(ucLogNetworkBuffer);
        ucLogNetworkBuffer = NULL;
    }

    if (pucLogQueueStorageArea) {
        heap_caps_free(pucLogQueueStorageArea);
        pucLogQueueStorageArea = NULL;
    }

    if (pxLogQueueStructure) {
        heap_caps_free(pxLogQueueStructure);
        pxLogQueueStructure = NULL;
    }

    ESP_LOGI(TAG, "Log MQTT manager stopped");
    return xRet;
}

BaseType_t xLogMqttPublish(const char *pTopicFilter,
                           uint16_t topicFilterLength,
                           const char *pPayload,
                           size_t payloadLength)
{
    MQTTStatus_t xMqttStatus;
    MQTTPublishInfo_t xPublishInfo;
    MQTTAgentCommandInfo_t xCommandParams;
    MQTTAgentCommandContext_t xCommandContext;

    if (!bLogMqttConnected) {
        ESP_LOGW(TAG, "Log MQTT not connected, cannot publish");
        return pdFAIL;
    }

    if (!pTopicFilter || !pPayload || topicFilterLength == 0 || payloadLength == 0) {
        ESP_LOGE(TAG, "Invalid publish parameters");
        return pdFAIL;
    }

    /* 设置发布信息 */
    memset(&xPublishInfo, 0x00, sizeof(xPublishInfo));
    xPublishInfo.qos = MQTTQoS1;
    xPublishInfo.retain = false;
    xPublishInfo.pTopicName = pTopicFilter;
    xPublishInfo.topicNameLength = topicFilterLength;
    xPublishInfo.pPayload = pPayload;
    xPublishInfo.payloadLength = payloadLength;

    /* 设置命令参数 */
    memset(&xCommandParams, 0x00, sizeof(xCommandParams));
    xCommandParams.blockTimeMs = 5000; /* 5秒超时 */
    xCommandParams.cmdCompleteCallback = NULL; /* 异步发送，不等待回调 */
    xCommandParams.pCmdCompleteCallbackContext = &xCommandContext;

    /* 发送发布命令 */
    xMqttStatus = MQTTAgent_Publish(&xLogMqttAgentContext,
                                    &xPublishInfo,
                                    &xCommandParams);

    if (xMqttStatus != MQTTSuccess) {
        ESP_LOGE(TAG, "Failed to send log publish command: %s",
                 MQTT_Status_strerror(xMqttStatus));
        return pdFAIL;
    }

    ESP_LOGD(TAG, "Log published to topic: %.*s", topicFilterLength, pTopicFilter);
    return pdPASS;
}

BaseType_t xLogMqttIsConnected(void)
{
    return bLogMqttConnected ? pdTRUE : pdFALSE;
}

MQTTAgentContext_t *pxGetLogMqttAgentContext(void)
{
    return &xLogMqttAgentContext;
}
